import apiClient from './apiClient';

class PassengerService {
  // Get all passengers
  async getAllPassengers() {
    try {
      const response = await apiClient.get('/passengers');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch passengers');
    }
  }

  // Get passenger by ID
  async getPassengerById(id) {
    try {
      const response = await apiClient.get(`/passengers/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch passenger');
    }
  }

  // Create new passenger
  async createPassenger(passengerData) {
    try {
      const response = await apiClient.post('/passengers', passengerData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to create passenger');
    }
  }

  // Update passenger
  async updatePassenger(id, passengerData) {
    try {
      const response = await apiClient.put(`/passengers/${id}`, passengerData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to update passenger');
    }
  }

  // Delete passenger
  async deletePassenger(id) {
    try {
      await apiClient.delete(`/passengers/${id}`);
      return true;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to delete passenger');
    }
  }

  // Get passengers by flight
  async getPassengersByFlight(flightId) {
    try {
      const response = await apiClient.get(`/passengers/flight/${flightId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch passengers by flight');
    }
  }

  // Get passengers by name
  async getPassengersByName(name) {
    try {
      const response = await apiClient.get(`/passengers/search/name/${name}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to search passengers by name');
    }
  }

  // Get passengers by passport
  async getPassengersByPassport(passportNumber) {
    try {
      const response = await apiClient.get(`/passengers/search/passport/${passportNumber}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to search passengers by passport');
    }
  }

  // Get passengers by phone
  async getPassengersByPhone(phoneNumber) {
    try {
      const response = await apiClient.get(`/passengers/search/phone/${phoneNumber}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to search passengers by phone');
    }
  }

  // Get passengers with services
  async getPassengersWithServices() {
    try {
      const response = await apiClient.get('/passengers/with-services');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch passengers with services');
    }
  }

  // Get passengers with special needs
  async getPassengersWithSpecialNeeds() {
    try {
      const response = await apiClient.get('/passengers/special-needs');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch passengers with special needs');
    }
  }

  // Check in passenger
  async checkInPassenger(id) {
    try {
      const response = await apiClient.post(`/passengers/${id}/checkin`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to check in passenger');
    }
  }
}

export default new PassengerService();
