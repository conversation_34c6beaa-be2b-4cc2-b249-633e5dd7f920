import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    proxy: {
      // Authentication service (backend1)
      '/api/auth': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false,
      },
      // Flight service
      '/api/flights': {
        target: 'http://localhost:8081',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api\/flights/, '/flights')
      },
      // Passenger service
      '/api/passengers': {
        target: 'http://localhost:8082',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api\/passengers/, '/passengers')
      },
      // User management service
      '/api/users': {
        target: 'http://localhost:8083',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api\/users/, '/users')
      },
      // Service management
      '/api/services': {
        target: 'http://localhost:8084',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api\/services/, '/services')
      },
      // Travel history service
      '/api/history': {
        target: 'http://localhost:8085',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api\/history/, '/history')
      },
      // Eureka server (for service discovery info)
      '/eureka': {
        target: 'http://localhost:8761',
        changeOrigin: true,
        secure: false,
      }
    }
  }
})
