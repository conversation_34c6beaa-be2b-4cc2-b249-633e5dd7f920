// Simple integration test script to verify microservices connectivity
import axios from 'axios';

const services = [
  { name: 'Eureka Server', url: 'http://localhost:8761/eureka/apps', method: 'GET' },
  { name: 'Backend1 (Auth)', url: 'http://localhost:8080/api/auth/login', method: 'POST', data: { username: 'admin', password: 'admin123' } },
  { name: 'Flights Service', url: 'http://localhost:8081/flights', method: 'GET' },
  { name: 'Passengers Service', url: 'http://localhost:8082/passengers', method: 'GET' },
  { name: 'User Management', url: 'http://localhost:8083/users', method: 'GET' },
  { name: 'Service Management', url: 'http://localhost:8084/services', method: 'GET' },
  { name: 'Travel History', url: 'http://localhost:8085/history/info', method: 'GET' }
];

async function testService(service) {
  try {
    const config = {
      method: service.method,
      url: service.url,
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (service.data) {
      config.data = service.data;
    }

    const response = await axios(config);
    console.log(`✅ ${service.name}: OK (Status: ${response.status})`);
    return true;
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log(`❌ ${service.name}: Service not running (Connection refused)`);
    } else if (error.response) {
      console.log(`⚠️  ${service.name}: HTTP ${error.response.status} - ${error.response.statusText}`);
    } else {
      console.log(`❌ ${service.name}: ${error.message}`);
    }
    return false;
  }
}

async function runTests() {
  console.log('🚀 Testing microservices connectivity...\n');
  
  let successCount = 0;
  
  for (const service of services) {
    const success = await testService(service);
    if (success) successCount++;
    await new Promise(resolve => setTimeout(resolve, 500)); // Small delay between tests
  }
  
  console.log(`\n📊 Results: ${successCount}/${services.length} services accessible`);
  
  if (successCount === services.length) {
    console.log('🎉 All services are running and accessible!');
  } else {
    console.log('⚠️  Some services are not accessible. Please check:');
    console.log('   1. All microservices are running');
    console.log('   2. Eureka server is running on port 8761');
    console.log('   3. Services are registered with Eureka');
    console.log('   4. No port conflicts');
  }
}

// Run the tests
runTests().catch(console.error);
